import { Request, Response } from "express";
import bcrypt from "bcryptjs";
import db from "../../config/db";
import { TABLE } from "../../utils/Database/table";
import { UserRole } from "../../utils/enums/users.enum";
import { sendResponse } from "../../utils/helperFunctions/responseHelper";
import asyncHandler from "../../middlewares/trycatch";
import validate from "../../validations";
import { createUserSchema } from "../../validations/user.validation";
import { sendEmployeeCredentialEmail } from "../../utils/services/nodemailer/employeeCredential";
import { addressSchema } from "../../validations/address.validation";
import { getIO } from "../../config/socket";
import { users } from "../notifications.controller";

// Create employee under a doctor with specific role_id
export const createEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Clone body for safe mutation
      const body = { ...req.body };

      const validationResult = validate(createUserSchema, body, res);
      if (!validationResult.success) {
        return;
      }

      const { first_name, last_name, email, username, role_id } =
        validationResult.data;

      // Check if doctor is authorized
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        sendResponse(res, 403, "Only doctors can create employees", false);
        return;
      }

      // Check if email already exists
      const existingUser = await db(TABLE.USERS).where({ email }).first();
      if (existingUser) {
        sendResponse(res, 400, "Email already exists", false);
        return;
      }

      // Check if username already exists
      if (username) {
        const existingUsername = await db(TABLE.USERS)
          .where({ username })
          .first();
        if (existingUsername) {
          sendResponse(res, 400, "Username already exists", false);
          return;
        }
      }

      // Get employee role ID if not provided
      let employeeRoleId = role_id;
      if (!employeeRoleId) {
        const employeeRole = await db(TABLE.ROLES)
          .where("role_name", UserRole.EMPLOYEE)
          .first();

        if (!employeeRole) {
          sendResponse(res, 400, "Employee role not found", false);
          return;
        }

        employeeRoleId = employeeRole.id;
      } else {
        // Verify the role_id exists and is valid for an employee
        const roleExists = await db(TABLE.ROLES)
          .where("id", employeeRoleId)
          .first();

        if (!roleExists) {
          sendResponse(res, 400, "Invalid role ID", false);
          return;
        }

        // Optional: You can restrict which roles a doctor can assign
        if (
          roleExists.role_name === UserRole.ADMIN ||
          roleExists.role_name === UserRole.SUPERADMIN
        ) {
          sendResponse(
            res,
            403,
            "You cannot create users with this role",
            false
          );
          return;
        }
      }

      // Generate random password
      const generateRandomPassword = () => {
        const characters =
          "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%&*";
        let password = "";
        for (let i = 0; i < 8; i++) {
          password += characters.charAt(
            Math.floor(Math.random() * characters.length)
          );
        }
        return password;
      };

      const password = generateRandomPassword();
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create employee user
      const newEmployee = await db(TABLE.USERS)
        .insert({
          first_name,
          last_name,
          email,
          username: username || null,
          password: hashedPassword,
          role_id: employeeRoleId,
          is_verified: true,
          is_active: true,
        })
        .returning([
          "id",
          "first_name",
          "last_name",
          "email",
          "username",
          "role_id",
        ]);

      // Create doctor-employee relationship
      await db(TABLE.DOCTOR_EMPLOYEES).insert({
        doctor_id: req.user.id,
        employee_id: newEmployee[0].id,
      });

      // Get role name for the response
      const role = await db(TABLE.ROLES).where("id", employeeRoleId).first();

      const responseData = {
        ...newEmployee[0],
        role: role.role_name,
      };

      // Send credentials email
      try {
        await sendEmployeeCredentialEmail({
          email,
          password,
          name: `${first_name} ${last_name}`,
          doctorName: `${req.user.first_name} ${req.user.last_name}`,
        });

        sendResponse(
          res,
          201,
          "Employee created successfully and credentials sent via email",
          true,
          responseData
        );
      } catch (emailError) {
        console.error("Failed to send email:", emailError);
        sendResponse(
          res,
          201,
          "Employee created successfully but failed to send email",
          true,
          responseData
        );
      }
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Get all employees under a doctor
export const getEmployees = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // Check if doctor is authorized
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        sendResponse(res, 403, "Only doctors can view their employees", false);
        return;
      }

      // Get all employees for this doctor
      const employees = await db(TABLE.USERS)
        .join(
          TABLE.DOCTOR_EMPLOYEES,
          `${TABLE.USERS}.id`,
          "=",
          `${TABLE.DOCTOR_EMPLOYEES}.employee_id`
        )
        .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
        .where(`${TABLE.DOCTOR_EMPLOYEES}.doctor_id`, req.user.id)
        .select([
          `${TABLE.USERS}.id`,
          `${TABLE.USERS}.first_name`,
          `${TABLE.USERS}.last_name`,
          `${TABLE.USERS}.email`,
          `${TABLE.USERS}.username`,
          `${TABLE.USERS}.profile_image`,
          `${TABLE.ROLES}.role_name as role`,
        ]);

      // Format profile image URLs
      const formattedEmployees = employees.map((employee) => {
        if (employee.profile_image) {
          employee.profile_image =
            process.env.BASE_URL + employee.profile_image;
        }
        return employee;
      });

      sendResponse(
        res,
        200,
        "Employees retrieved successfully",
        true,
        formattedEmployees
      );
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Delete an employee under a doctor
export const deleteEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { employeeId } = req.params;

      // Check if doctor is authorized
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        sendResponse(
          res,
          403,
          "Only doctors can delete their employees",
          false
        );
        return;
      }

      // At this point, we know req.user exists and is a doctor
      const doctorId = req.user.id;

      // Verify this employee belongs to the doctor
      const relationship = await db(TABLE.DOCTOR_EMPLOYEES)
        .where({
          doctor_id: doctorId,
          employee_id: employeeId,
        })
        .first();

      if (!relationship) {
        sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
        return;
      }

      // Begin transaction
      await db.transaction(async (trx) => {
        // First remove the relationship
        await trx(TABLE.DOCTOR_EMPLOYEES)
          .where({
            doctor_id: doctorId,
            employee_id: employeeId,
          })
          .delete();

        // Then delete the user
        await trx(TABLE.USERS).where("id", employeeId).delete();
      });

      sendResponse(res, 200, "Employee deleted successfully", true);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
// Update an employee under a doctor
export const updateEmployee = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { employeeId } = req.params;
      const { first_name, last_name, email, username, role_id } = req.body;

      // Check if doctor is authorized
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        sendResponse(
          res,
          403,
          "Only doctors can update their employees",
          false
        );
        return;
      }

      const doctorId = req.user.id;

      // Verify this employee belongs to the doctor
      const relationship = await db(TABLE.DOCTOR_EMPLOYEES)
        .where({
          doctor_id: doctorId,
          employee_id: employeeId,
        })
        .first();

      if (!relationship) {
        sendResponse(
          res,
          404,
          "Employee not found or doesn't belong to you",
          false
        );
        return;
      }

      // Check if email is being updated and if it already exists
      if (email) {
        const existingUser = await db(TABLE.USERS)
          .where({ email })
          .whereNot("id", employeeId)
          .first();

        if (existingUser) {
          sendResponse(res, 400, "Email already exists", false);
          return;
        }
      }

      // Check if username is being updated and if it already exists
      if (username) {
        const existingUsername = await db(TABLE.USERS)
          .where({ username })
          .whereNot("id", employeeId)
          .first();

        if (existingUsername) {
          sendResponse(res, 400, "Username already exists", false);
          return;
        }
      }

      // Update employee information
      const updateData: any = {};
      if (first_name) updateData.first_name = first_name;
      if (last_name) updateData.last_name = last_name;
      if (email) updateData.email = email;
      if (username) updateData.username = username;

      // Handle role_id update if provided
      if (role_id) {
        // Verify the role_id exists and is valid for an employee
        const roleExists = await db(TABLE.ROLES).where("id", role_id).first();

        if (!roleExists) {
          sendResponse(res, 400, "Invalid role ID", false);
          return;
        }

        // Restrict which roles a doctor can assign
        if (
          roleExists.role_name === UserRole.ADMIN ||
          roleExists.role_name === UserRole.SUPERADMIN ||
          roleExists.role_name === UserRole.DOCTOR
        ) {
          sendResponse(
            res,
            403,
            "You cannot assign this role to an employee",
            false
          );
          return;
        }

        updateData.role_id = role_id;
      }

      // Only update if there are fields to update
      if (Object.keys(updateData).length > 0) {
        const updatedEmployee = await db(TABLE.USERS)
          .where("id", employeeId)
          .update(updateData)
          .returning([
            "id",
            "first_name",
            "last_name",
            "email",
            "username",
            "role_id",
          ]);

        // Get role name for the response
        const role = await db(TABLE.ROLES)
          .where("id", updatedEmployee[0].role_id)
          .first();

        const responseData = {
          ...updatedEmployee[0],
          role: role.role_name,
        };

        sendResponse(
          res,
          200,
          "Employee updated successfully",
          true,
          responseData
        );
      } else {
        sendResponse(res, 400, "No fields to update", false);
      }
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const addDoctorAddress = asyncHandler(
  async (req: Request, res: Response) => {
    // Check if doctor is authorized
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      sendResponse(res, 403, "Only doctors can add addresses", false);
      return;
    }

    const validationResult = validate(addressSchema, req.body, res);
    if (!validationResult.success) {
      return;
    }

    const [address] = await db("doctor_addresses")
      .insert({
        doctor_id: req.user.id,
        ...validationResult.data,
      })
      .returning("*");

    sendResponse(res, 201, "Address added successfully", true, address);
  }
);
// Get all doctor addresses
export const getDoctorAddresses = asyncHandler(
  async (req: Request, res: Response) => {
    // Check if doctor is authorized
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      sendResponse(res, 403, "Only doctors can view addresses", false);
      return;
    }

    const addresses = await db("doctor_addresses")
      .where("doctor_id", req.user.id)
      .select("*")
      .orderBy("created_at", "desc");

    sendResponse(res, 200, "Addresses retrieved", true, addresses);
  }
);
export const updateDoctorAddress = asyncHandler(
  async (req: Request, res: Response) => {
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      sendResponse(res, 403, "Only doctors can update addresses", false);
      return;
    }

    const { addressId } = req.params;

    // Verify address belongs to this doctor
    const address = await db("doctor_addresses")
      .where({
        id: addressId,
        doctor_id: req.user.id,
      })
      .first();

    if (!address) {
      sendResponse(
        res,
        404,
        "Address not found or doesn't belong to you",
        false
      );
      return;
    }

    const validationResult = validate(addressSchema, req.body, res);
    if (!validationResult.success) {
      return;
    }

    const [updatedAddress] = await db("doctor_addresses")
      .where("id", addressId)
      .update(validationResult.data)
      .returning("*");

    sendResponse(
      res,
      200,
      "Address updated successfully",
      true,
      updatedAddress
    );
  }
);
export const deleteDoctorAddress = asyncHandler(
  async (req: Request, res: Response) => {
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      sendResponse(res, 403, "Only doctors can delete addresses", false);
      return;
    }
    const { addressId } = req.params;
    // Verify address belongs to this doctor
    const address = await db("doctor_addresses")
      .where({
        id: addressId,
        doctor_id: req.user.id,
      })
      .first();

    if (!address) {
      sendResponse(
        res,
        404,
        "Address not found or doesn't belong to you",
        false
      );
      return;
    }

    await db("doctor_addresses").where("id", addressId).delete();

    sendResponse(res, 200, "Address deleted successfully", true);
  }
);
export const savePatientStep = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const data: any = req.body;
      const step = ["step1", "step2", "step3", "step4"].includes(data.step)
        ? data.step
        : undefined;
      if (!step)
        return sendResponse(res, 400, "Invalid or missing step", false);

      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can create or update patients",
          false
        );
      }

      const patientId = data.id ? Number(data.id) : undefined;

      // === STEP 1 ===
      if (step === "step1") {
  const required = [
    "first_name",
    "last_name",
    "email",
    "plan_id",
    "dob",
    "gender",
    "ship_to_office",
    "bill_to_office",
  ];
  const missing = required.filter((f) => data[f] == null || data[f] === "");
  if (missing.length)
    return sendResponse(
      res,
      400,
      `Missing required fields: ${missing.join(", ")}`,
      false
    );

  const plan = await db(TABLE.PLANS).where({ id: data.plan_id }).first();
  if (!plan) return sendResponse(res, 400, "Invalid plan_id", false);

  const shipAddr = await db(TABLE.DOCTOR_ADDRESSES)
    .where({ id: data.ship_to_office, doctor_id: req.user.id })
    .first();
  if (!shipAddr)
    return sendResponse(res, 400, "Invalid ship_to_office", false);

  const billAddr = await db(TABLE.DOCTOR_ADDRESSES)
    .where({ id: data.bill_to_office, doctor_id: req.user.id })
    .first();
  if (!billAddr)
    return sendResponse(res, 400, "Invalid bill_to_office", false);

  let patient;

  if (patientId) {
    // update
    await db(TABLE.PATIENTS)
      .where({ id: patientId, doctor_id: req.user.id })
      .update({
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        plan_id: data.plan_id,
        dob: data.dob,
        gender: data.gender,
        ship_to_office: data.ship_to_office,
        bill_to_office: data.bill_to_office,
      });

    patient = await db(TABLE.PATIENTS)
      .where({ id: patientId, doctor_id: req.user.id })
      .first();

    return sendResponse(res, 200, "Step 1 updated", true, patient);
  } else {
    // create
    const [newPat] = await db(TABLE.PATIENTS)
      .insert({
        doctor_id: req.user.id,
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        plan_id: data.plan_id,
        dob: data.dob,
        gender: data.gender,
        ship_to_office: data.ship_to_office,
        bill_to_office: data.bill_to_office,
      })
      .returning("*");

    return sendResponse(res, 201, "Step 1 created", true, newPat);
  }
}


      // === STEP 2 ===
      if (step === "step2") {
        if (!patientId)
          return sendResponse(res, 400, "Missing patient id", false);

        const pat = await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: req.user.id })
          .first();
        if (!pat) return sendResponse(res, 404, "Patient not found", false);

        const plan = pat.plan_id
          ? await db(TABLE.PLANS).where({ id: pat.plan_id }).first()
          : null;

        if (plan && plan.name === "4D Graphy Retainer") {
          return sendResponse(
            res,
            200,
            "Step 2 skipped for selected plan",
            true,
            pat
          );
        }

        await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: req.user.id })
          .update({
            clinical_conditions: data.clinical_conditions,
            general_notes: data.general_notes,
          });
        return sendResponse(res, 200, "Step 2 saved", true);
      }

      // === STEP 3 ===
      if (step === "step3") {
        if (!patientId)
          return sendResponse(res, 400, "Missing patient id", false);

        const pat = await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: req.user.id })
          .first();
        if (!pat) return sendResponse(res, 404, "Patient not found", false);

        const plan = pat.plan_id
          ? await db(TABLE.PLANS).where({ id: pat.plan_id }).first()
          : null;

        if (!req.files || typeof req.files !== "object") {
          return sendResponse(res, 400, "Files are required for step3", false);
        }

        const files = req.files as unknown as Record<
          string,
          Express.Multer.File[]
        >;
        const updatePayload: any = {};

        const requiredFields = ["stlFile1", "stlFile2"];
        const optionalFields = [
          "profileRepose",
          "buccalRight",
          "buccalLeft",
          "frontalRepose",
          "frontalSmiling",
          "labialAnterior",
          "occlussalLower",
          "occlussalUpper",
          "cbctFile",
        ];
        if (!plan || plan.name !== "4D Graphy Retainer") {
          optionalFields.push("radioGraph1", "radioGraph2");
        }

        [...requiredFields, ...optionalFields].forEach((field) => {
          const arr = files[field];
          if (arr && arr.length) updatePayload[field] = arr[0].filename;
        });

        const missingRequired = requiredFields.filter((f) => !updatePayload[f]);
        if (missingRequired.length) {
          return sendResponse(
            res,
            400,
            `Missing required files: ${missingRequired.join(", ")}`,
            false
          );
        }

        await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: req.user.id })
          .update(updatePayload);

        const updatedPat = await db(TABLE.PATIENTS)
          .where({ id: patientId })
          .first();
        return sendResponse(res, 200, "Step 3 saved", true, updatedPat);
      }

      // === STEP 4 ===
      if (step === "step4") {
        const isNew = !patientId;
        if (isNew && !data.case_prescription) {
          return sendResponse(
            res,
            400,
            "Missing case_prescription for new patient",
            false
          );
        }
        if (!isNew && !data.case_prescription) {
          return sendResponse(res, 400, "Missing case_prescription", false);
        }

        // parse JSON
        let step4Json: any;
        try {
          step4Json =
            typeof data.case_prescription === "string"
              ? JSON.parse(data.case_prescription)
              : data.case_prescription;
        } catch {
          return sendResponse(
            res,
            400,
            "Invalid JSON in case prescription",
            false
          );
        }

        // --- NEW PATIENT FLOW ---
        if (isNew) {
          const [newPat] = await db(TABLE.PATIENTS)
            .insert({
              doctor_id: req.user.id,
              first_name: data.first_name,
              last_name: data.last_name,
              email: data.email,
              plan_id: data.plan_id,
              dob: data.dob,
              gender: data.gender,
              ship_to_office: data.ship_to_office,
              bill_to_office: data.bill_to_office,
              data: JSON.stringify(step4Json),
            })
            .returning("*");

          await db(TABLE.PATIENTS_VERSIONS).insert({
            patient_id: newPat.id,
            created_by: req.user.id,
            version_number: 1,
            title: "Initial V1",
            status: "sent_by_doctor",
            upper_steps: data.upper_steps || null,
            lower_steps: data.lower_steps || null,
            approval_reason: null,
            rejection_reason: null,
            is_latest_version: true,
            data: JSON.stringify({ patient: newPat }),
          });

          return sendResponse(
            res,
            201,
            "Step 4 created (initial)",
            true,
            newPat
          );
        }

        // --- EXISTING PATIENT FLOW ---
        const pat = await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: req.user.id })
          .first();
        if (!pat) return sendResponse(res, 404, "Patient not found", false);

        await db(TABLE.PATIENTS)
          .where({ id: patientId, doctor_id: req.user.id })
          .update({ data: JSON.stringify(step4Json) });

        const lastVer = await db(TABLE.PATIENTS_VERSIONS)
          .where({ patient_id: patientId })
          .orderBy("version_number", "desc")
          .first();

        const nextVerNum = lastVer ? Number(lastVer.version_number) + 1 : 1;

        await db(TABLE.PATIENTS_VERSIONS)
          .where({ patient_id: patientId })
          .update({ is_latest_version: false });

        await db(TABLE.PATIENTS_VERSIONS).insert({
          patient_id: patientId,
          created_by: req.user.id,
          version_number: nextVerNum,
          title: `Initial V${nextVerNum}`,
          status: "sent_by_doctor",
          upper_steps: data.upper_steps || null,
          lower_steps: data.lower_steps || null,
          approval_reason: null,
          rejection_reason: null,
          is_latest_version: true,
          data: JSON.stringify(step4Json),
        });

        let specialist;
        const userWithSpecialist = req.user as typeof req.user & { specialist_id?: number };
        if (userWithSpecialist.specialist_id) {
          // First try to get the assigned specialist
          specialist = await db(TABLE.USERS)
            .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
            .where(`${TABLE.USERS}.id`, userWithSpecialist.specialist_id)
            .andWhere(`${TABLE.USERS}.is_active`, true)
            .select(`${TABLE.USERS}.*`)
            .first();
        }

        // If no assigned specialist or not found, fall back to any active specialist
        if (!specialist) {
          specialist = await db(TABLE.USERS)
            .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, `${TABLE.ROLES}.id`)
            .where(`${TABLE.ROLES}.role_name`, "specialist")
            .andWhere(`${TABLE.USERS}.is_active`, true)
            .select(`${TABLE.USERS}.*`)
            .first();
        }

        if (specialist) {
          const [notification] = await db(TABLE.NOTIFICATIONS)
            .insert({
              receiver_id: specialist.id,
              user_id: req.user.id,
              title: "Patient File Ready for Review",
              message: `Patient file submitted by Dr. ${req.user.first_name}`,
              details: JSON.stringify({
                type: "patient_file_ready",
                doctor_id: req.user.id,
                patient_id: patientId,
              }),
            })
            .returning("*");

          const io = getIO();
          const userSockets = users.find(
            (u) => u.userId === String(specialist.id)
          );
          if (userSockets) {
            const unreadCount = await db(TABLE.NOTIFICATIONS)
              .where({ receiver_id: specialist.id, is_read: false })
              .count("id as count")
              .first()
              .then((r: any) => parseInt(r.count) || 0);

            userSockets.socketIds.forEach((sid) =>
              io.to(sid).emit("notification", {
                notifications: [notification],
                unread_count: unreadCount,
              })
            );
          }
        }

        return sendResponse(res, 200, "Step 4 saved", true);
      }

      // unsupported
      return sendResponse(res, 400, "Unsupported step", false);
    } catch (error: any) {
      console.error(error);
      return sendResponse(
        res,
        500,
        error.message || "Something went wrong",
        false
      );
    }
  }
);
export const getPatientById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const patientId = Number(req.params.id);
      if (!patientId) {
        return sendResponse(res, 400, "Patient ID is required", false);
      }

      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can access patient data",
          false
        );
      }

      // 1. Patient basic info
      const patient = await db(TABLE.PATIENTS)
        .where({ id: patientId, doctor_id: req.user.id })
        .first();

      if (!patient) {
        return sendResponse(res, 404, "Patient not found", false);
      }

      // 2. Plan info
      let plan = null;
      if (patient.plan_id) {
        plan = await db(TABLE.PLANS).where({ id: patient.plan_id }).first();
      }

      // 3. Doctor addresses
      let shipToOffice = null, billToOffice = null;
      if (patient.ship_to_office) {
        shipToOffice = await db(TABLE.DOCTOR_ADDRESSES)
          .where({ id: patient.ship_to_office })
          .first();
      }
      if (patient.bill_to_office) {
        billToOffice = await db(TABLE.DOCTOR_ADDRESSES)
          .where({ id: patient.bill_to_office })
          .first();
      }

      // 4. Related tables
      const refinements = await db(TABLE.REFINEMENTS).where({ patient_id: patientId });
      const alignerReplacements = await db("aligner_replacements").where({ patient_id: patientId });
      const retainers = await db("four_d_graphy_retainers").where({ patient_id: patientId });
      const refinementsAligner = await db("refinements_aligner").where({ patient_id: patientId });
      const patientFiles = await db("patient_files").where({ patient_id: patientId });

      // 5. Parse clinical conditions
      let clinicalConditions: string[] | null = null;
      if (patient.clinical_conditions) {
        try {
          clinicalConditions = JSON.parse(patient.clinical_conditions);
        } catch {
          clinicalConditions = null;
        }
      }

      // 6. File fields with URLs
      const baseUrl = process.env.BASE_URL;
      const fileFields = [
        "stlFile1",
        "stlFile2",
        "cbctFile",
        "radioGraph1",
        "radioGraph2",
        "profileRepose",
        "buccalRight",
        "buccalLeft",
        "frontalRepose",
        "frontalSmiling",
        "labialAnterior",
        "occlussalLower",
        "occlussalUpper",
      ];

      const enrichedPatient: any = {
        ...patient,
        clinical_conditions: clinicalConditions,
        plan,
        shipToOffice,
        billToOffice,
        refinements,
        alignerReplacements,
        retainers,
        refinementsAligner,
        patientFiles,
      };

      for (const field of fileFields) {
        if (patient[field]) {
          enrichedPatient[field] = `${baseUrl}/patients/${patient[field]}`;
        }
      }

      // For retainers
      enrichedPatient.retainers = retainers.map((r) => ({
        ...r,
        stl_file1: r.stl_file1 ? `${baseUrl}/patients/${r.stl_file1}` : null,
        stl_file2: r.stl_file2 ? `${baseUrl}/patients/${r.stl_file2}` : null,
        cbct_file: r.cbct_file ? `${baseUrl}/patients/${r.cbct_file}` : null,
        profile_repose: r.profile_repose ? `${baseUrl}/patients/${r.profile_repose}` : null,
        buccal_right: r.buccal_right ? `${baseUrl}/patients/${r.buccal_right}` : null,
        buccal_left: r.buccal_left ? `${baseUrl}/patients/${r.buccal_left}` : null,
        frontal_repose: r.frontal_repose ? `${baseUrl}/patients/${r.frontal_repose}` : null,
        frontal_smiling: r.frontal_smiling ? `${baseUrl}/patients/${r.frontal_smiling}` : null,
        labial_anterior: r.labial_anterior ? `${baseUrl}/patients/${r.labial_anterior}` : null,
        occlusal_lower: r.occlusal_lower ? `${baseUrl}/patients/${r.occlusal_lower}` : null,
        occlusal_upper: r.occlusal_upper ? `${baseUrl}/patients/${r.occlusal_upper}` : null,
        radiograph1: r.radiograph1 ? `${baseUrl}/patients/${r.radiograph1}` : null,
        radiograph2: r.radiograph2 ? `${baseUrl}/patients/${r.radiograph2}` : null,
      }));

      // For refinementsAligner
      enrichedPatient.refinementsAligner = refinementsAligner.map((r) => ({
        ...r,
        upper_impression: r.upper_impression ? `${baseUrl}/patients/${r.upper_impression}` : null,
        lower_impression: r.lower_impression ? `${baseUrl}/patients/${r.lower_impression}` : null,
        profileRepose: r.profileRepose ? `${baseUrl}/patients/${r.profileRepose}` : null,
        buccalRight: r.buccalRight ? `${baseUrl}/patients/${r.buccalRight}` : null,
        buccalLeft: r.buccalLeft ? `${baseUrl}/patients/${r.buccalLeft}` : null,
        frontalRepose: r.frontalRepose ? `${baseUrl}/patients/${r.frontalRepose}` : null,
        frontalSmiling: r.frontalSmiling ? `${baseUrl}/patients/${r.frontalSmiling}` : null,
        labialAnterior: r.labialAnterior ? `${baseUrl}/patients/${r.labialAnterior}` : null,
        occlussalLower: r.occlussalLower ? `${baseUrl}/patients/${r.occlussalLower}` : null,
        occlussalUpper: r.occlussalUpper ? `${baseUrl}/patients/${r.occlussalUpper}` : null,
        radioGraph1: r.radioGraph1 ? `${baseUrl}/patients/${r.radioGraph1}` : null,
        radioGraph2: r.radioGraph2 ? `${baseUrl}/patients/${r.radioGraph2}` : null,
      }));

      // For patientFiles
      enrichedPatient.patientFiles = patientFiles.map((f) => ({
        ...f,
        file_name: f.file_name ? `${baseUrl}/patients/${f.file_name}` : null,
      }));

      return sendResponse(
        res,
        200,
        "Patient fetched successfully",
        true,
        enrichedPatient
      );
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllPatientsForDoctor = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(
          res,
          403,
          "Only doctors can access patient list",
          false
        );
      }

      const patients = await db(TABLE.PATIENTS)
        .where({ doctor_id: req.user.id })
        .orderBy("created_at", "desc");

      const baseUrl = process.env.BASE_URL;

      const fileFields = [
        "stlFile1",
        "stlFile2",
        "cbctFile",
        "radioGraph1",
        "radioGraph2",
        "profileRepose",
        "buccalRight",
        "buccalLeft",
        "frontalRepose",
        "frontalSmiling",
        "labialAnterior",
        "occlussalLower",
        "occlussalUpper",
      ];

      const enrichedPatients = await Promise.all(
        patients.map(async (patient) => {
          let planName = null;
          if (patient.plan_id) {
            const plan = await db(TABLE.PLANS)
              .where({ id: patient.plan_id })
              .first();
            if (plan) planName = plan.name;
          }

          let clinicalConditions: string[] | null = null;
          if (patient.clinical_conditions) {
            try {
              clinicalConditions = JSON.parse(patient.clinical_conditions);
            } catch {
              clinicalConditions = null;
            }
          }

          const enriched = {
            ...patient,
            plan_name: planName,
            clinical_conditions: clinicalConditions,
          };

          for (const field of fileFields) {
            if (patient[field]) {
              enriched[field] = `${baseUrl}/patients/${patient[field]}`;
            }
          }

          return enriched;
        })
      );

      return sendResponse(
        res,
        200,
        "Patients fetched successfully",
        true,
        enrichedPatients
      );
    } catch (error: any) {
      console.error(error);
      return sendResponse(res, 500, error.message, false);
    }
  }
);
export const uploadCbctFile = asyncHandler(
  async (req: Request, res: Response) => {
    // Verify doctor authentication
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      return sendResponse(
        res,
        403,
        "Only doctors can upload CBCT files",
        false
      );
    }

    const { patientId } = req.params;
    const { reason } = req.body; // Get reason from request body

    // Verify patient exists and belongs to doctor
    const patient = await db(TABLE.PATIENTS)
      .where({ id: patientId, doctor_id: req.user.id })
      .first();

    if (!patient) {
      return sendResponse(res, 404, "Patient not found or unauthorized", false);
    }

    // Check file exists
    if (!req.file) {
      return sendResponse(res, 400, "CBCT file is required", false);
    }

    // Save to CBCT table with reason
    const [cbctFile] = await db("patient_files")
      .insert({
        patient_id: patientId,
        file_name: req.file.filename,
        reason: reason || null, // Store reason if provided
      })
      .returning("*");

    sendResponse(res, 201, "CBCT uploaded successfully", true, {
      ...cbctFile,
      file_url: `${process.env.BASE_URL}/cbct/${cbctFile.file_name}`,
    });
  }
);
export const uploadRefinements = asyncHandler(
  async (req: Request, res: Response) => {
    // Only doctors
    if (!req.user || req.user.role !== UserRole.DOCTOR) {
      return sendResponse(
        res,
        403,
        "Only doctors can upload refinements",
        false
      );
    }

    const patientId = Number(req.params.patientId);
    const { refinementDetails } = req.body;

    // Verify patient
    const patient = await db(TABLE.PATIENTS)
      .where({ id: patientId, doctor_id: req.user.id })
      .first();
    if (!patient) {
      return sendResponse(res, 404, "Patient not found or unauthorized", false);
    }

    // Ensure both impression files exist
    if (
      !req.files ||
      !(req.files as any).upperImpression ||
      !(req.files as any).lowerImpression
    ) {
      return sendResponse(
        res,
        400,
        "Both upperImpression and lowerImpression files are required",
        false
      );
    }

    // Properly cast multer's FileArray to Record<string, File[]>
    const files = req.files as unknown as Record<string, Express.Multer.File[]>;

    // Build payload
    const payload: any = {
      patient_id: patientId,
      refinement_details: refinementDetails || null,
      upper_impression: files.upperImpression[0].filename,
      lower_impression: files.lowerImpression[0].filename,
    };

    // Optional imaging fields
    const imageFields = [
      "profileRepose",
      "buccalRight",
      "buccalLeft",
      "frontalRepose",
      "frontalSmiling",
      "labialAnterior",
      "occlussalLower",
      "occlussalUpper",
      "radioGraph1",
      "radioGraph2",
    ];
    for (const field of imageFields) {
      if (files[field]) {
        payload[field] = files[field][0].filename;
      }
    }

    // Insert into refinements_aligner
    const [record] = await db("refinements_aligner")
      .insert(payload)
      .returning("*");

    return sendResponse(res, 201, "Refinements uploaded", true, record);
  }
);
export const createAlignerReplacement = asyncHandler(
  async (req: Request, res: Response) => {
    const { patient_id, replacement_data } = req.body;

    // Field-specific validation
    if (!patient_id) {
      return res.status(400).json({
        success: false,
        field: "patient_id",
        message: "Patient ID is required.",
      });
    }

    if (!replacement_data) {
      return res.status(400).json({
        success: false,
        field: "replacement_data",
        message: "Replacement note is required.",
      });
    }

    // Insert into database
    const [inserted] = await db("aligner_replacements")
      .insert({
        patient_id,
        replacement: replacement_data,
      })
      .returning("id");

    res.status(201).json({
      success: true,
      message: "Aligner replacement added",
      patient_id,
      replacement_data
    });
  }
);
export const requestRetainer = asyncHandler(
  async (req: Request, res: Response) => {
    const { patient_id, other_details, mode } = req.body;

    // 1) Validate
    if (!patient_id || !["upload", "use_last"].includes(mode)) {
      return sendResponse(res, 400, "Invalid payload", false);
    }

    // 2) MODE = use_last → fetch latest row
    if (mode === "use_last") {
      const last = await db("four_d_graphy_retainers")
        .where({ patient_id })
        .orderBy("created_at", "desc")
        .first();

      if (!last) {
        return sendResponse(
          res,
          404,
          "No previous retainer scans found",
          false
        );
      }
      return sendResponse(res, 200, "Last scans fetched", true, last);
    }

    // 3) MODE = upload → read files from req.files
    const files = req.files as unknown as Record<string, Express.Multer.File[]>;

    // map each possible upload into its own variable
    const stl_file1 = files.stlFile1?.[0]?.filename || null;
    const stl_file2 = files.stlFile2?.[0]?.filename || null;
    const cbct_file = files.cbctFile?.[0]?.filename || null;
    const profile_repose = files.profileRepose?.[0]?.filename || null;
    const buccal_right = files.buccalRight?.[0]?.filename || null;
    const buccal_left = files.buccalLeft?.[0]?.filename || null;
    const frontal_repose = files.frontalRepose?.[0]?.filename || null;
    const frontal_smiling = files.frontalSmiling?.[0]?.filename || null;
    const labial_anterior = files.labialAnterior?.[0]?.filename || null;
    const occlusal_lower = files.occlussalLower?.[0]?.filename || null;
    const occlusal_upper = files.occlussalUpper?.[0]?.filename || null;
    const radiograph1 = files.radioGraph1?.[0]?.filename || null;
    const radiograph2 = files.radioGraph2?.[0]?.filename || null;

    // require at least one file
    const anyUploaded = [
      stl_file1,
      stl_file2,
      cbct_file,
      profile_repose,
      buccal_right,
      buccal_left,
      frontal_repose,
      frontal_smiling,
      labial_anterior,
      occlusal_lower,
      occlusal_upper,
      radiograph1,
      radiograph2,
    ].some((f) => f !== null);

    if (!anyUploaded) {
      return sendResponse(
        res,
        400,
        "Upload at least one STL or photo file",
        false
      );
    }

    // 4) Insert new row
    const [inserted] = await db("four_d_graphy_retainers")
      .insert({
        patient_id,
        other_details: other_details || null,
        stl_file1,
        stl_file2,
        cbct_file,
        profile_repose,
        buccal_right,
        buccal_left,
        frontal_repose,
        frontal_smiling,
        labial_anterior,
        occlusal_lower,
        occlusal_upper,
        radiograph1,
        radiograph2,
      })
      .returning("*");

    return sendResponse(res, 201, "New scans saved", true, inserted);
  }
);
export const reviewSharedLinkVersion = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      // 1) Only doctors can review
      if (!req.user || req.user.role !== UserRole.DOCTOR) {
        return sendResponse(res, 403, "Only doctors can review shared versions", false);
      }

      // 2) Extract versionId
      const { versionId } = req.params;
      if (!versionId) {
        return sendResponse(res, 400, "versionId is required", false);
      }

      // 3) Extract action + reason
      const { action, reason } = req.body;
      if (!["approve", "reject"].includes(action)) {
        return sendResponse(res, 400, "Action must be 'approve' or 'reject'", false);
      }

      // 4) Fetch version
      const version = await db(TABLE.PATIENTS_VERSIONS).where({ id: versionId }).first();
      if (!version) {
        return sendResponse(res, 404, "Version not found", false);
      }

      // 5) Only versions shared by specialist can be reviewed by doctor
      if (version.status !== "sent_by_specialist") {
        return sendResponse(res, 400, "Only shared link versions can be reviewed by doctor", false);
      }

      // === Approve
      if (action === "approve") {
        await db(TABLE.PATIENTS_VERSIONS)
          .where({ id: versionId })
          .update({
            status: "approved_by_doctor",
            approval_reason: reason || null,
          });

        return sendResponse(res, 200, "Version approved by doctor", true);
      }

      // === Reject
      if (!reason) {
        return sendResponse(res, 400, "Rejection reason is required", false);
      }

      await db(TABLE.PATIENTS_VERSIONS)
        .where({ id: versionId })
        .update({
          status: "rejected_by_doctor",
          rejection_reason: reason,
        });

      return sendResponse(res, 200, "Version rejected by doctor", true);
    } catch (error: any) {
      console.error("reviewSharedLinkVersion error:", error);
      return sendResponse(res, 500, error.message || "Something went wrong", false);
    }
  }
);
  