import { Router } from "express";
import AUTHROUT<PERSON> from "./auth.routes";
import DOCTOR<PERSON>UT<PERSON> from "./doctor.routes";
import PERMISSIONROUTES from "./permission.routes";
import SPECIALISTROUTES from "./specialist.routes";
import ADMINPLANROUTES from "./admin/plan.routes";
import USERROUTES from "./admin/user.routes";
const router = Router();

router.use("/auth", AUTHROUTES);
router.use("/admin", ADMINPLANROUTES);
router.use("/doctor", DOCTORROUTES);
router.use("/permissions", PERMISSIONROUTES);
router.use("/specialist", SPECIALISTROUTES);
router.use("/user", USERROUTES);

export default router;
