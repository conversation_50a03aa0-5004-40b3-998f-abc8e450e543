import dotenv from "dotenv";
dotenv.config();

// Generate allowed origins for ports 3000-3008
const generateLocalOrigins = (): string[] => {
  const origins: string[] = [];
  for (let port = 3000; port <= 3008; port++) {
    origins.push(`http://localhost:${port}`);
  }
  return origins;
};

const allowedOrigins: string[] = [
  process.env.FRONTEND_URL ?? "",
  process.env.BASE_URL ?? "",
  process.env.ADMIN_BASE_URL ?? "",
  ...generateLocalOrigins(), // Add ports 3000-3008
].filter((origin) => origin.trim() !== "");

const corsOptions = {
  origin: allowedOrigins.length > 0 ? allowedOrigins : "*",
  methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
  credentials: true,
};

export default corsOptions;
