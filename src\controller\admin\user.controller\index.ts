import { Request, Response } from "express";
import async<PERSON>and<PERSON> from "../../../middlewares/trycatch";
import bcrypt from "bcryptjs";
import { sendResponse } from "../../../utils/helperFunctions/responseHelper";
import { UserRole } from "../../../utils/enums/users.enum";
import validate from "../../../validations";
import {
  updateUserSchema,
} from "../../../validations/user.validation";
import { TABLE } from "../../../utils/Database/table";
import db from "../../../config/db";


// Login User
export const createUser = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      // Validate input, get fields from req.body
      const { first_name, last_name, email, password, role_id, specialist_id } = req.body;

      // Check if email already exists
      const existingUser = await db(TABLE.USERS).where({ email }).first();
      if (existingUser) {
        sendResponse(res, 400, "Email already exists", false);
        return;
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Doctor create karte waqt specialist_id bhi save karein
      const [newUser] = await db(TABLE.USERS)
        .insert({
          first_name,
          last_name,
          email,
          password: hashedPassword,
          role: role_id,
          specialist_id: role_id === UserRole.DOCTOR ? specialist_id : null, // sirf doctor ke liye
        })
        .returning([
          "id",
          "first_name",
          "last_name",
          "email",
          "role",
          "specialist_id",
        ]);

      sendResponse(res, 201, "User created successfully", true, newUser);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

export const updateUser = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const validationResult = validate(updateUserSchema, req.body, res);

      if (!validationResult.success) {
        sendResponse(res, 400, "Validation error", false);
        return;
      }

      const { first_name, last_name, email, role_id } = validationResult.data;

      // Check if the email is unique (exclude the current user by their ID)
      if (email) {
        const existingUser = await db(TABLE.USERS)
          .where({ email: email })
          .whereNot("id", id)
          .first();

        if (existingUser) {
          sendResponse(res, 400, "Email is already exists", false);
          return;
        }
      }

      const updatedUser = await db(TABLE.USERS)
        .where({ id }) // Find user by ID
        .update({
          first_name,
          last_name,
          email,
          role: role_id || UserRole.DOCTOR,
        })
        .returning([
          "id",
          "first_name",
          "last_name",
          "email",
          "role",
          "is_active",
          "is_verified",
        ]);

      if (!updatedUser) {
        sendResponse(res, 400, "User not found", false);
        return;
      }

      sendResponse(res, 200, "User updated successfully", true, updatedUser);
      return;
    } catch (error: any) {
      console.log(error);
      sendResponse(res, 500, error.message, false);
      return;
    }
  }
);

export const getAllUsers = asyncHandler(async (req: Request, res: Response) => {
  try {
      console.log("Update User Request Body:", req.body);

    const { page, limit } = req.query;

    const pageSize = parseInt(page as string) || 1;
    const pageLimit = parseInt(limit as string) || 10;
    const skip = (pageSize - 1) * pageLimit;

    // Fetch paginated users based on admin's user ID
    const users = await db(TABLE.USERS)
      .select(
        "id",
        "first_name",
        "last_name",
        "email",
        "role",
        "is_active",
        "is_verified"
      )
      .offset(skip)
      .limit(pageLimit)
      .orderBy("created_at", "desc");

    // Fetch the total count for pagination
    const totalCountResult = await db(TABLE.USERS).count({ count: "*" });

    const totalCount = parseInt(totalCountResult[0]?.count as string) || 0;

    const data = {
      users,
      page: pageSize,
      limit: pageLimit,
      totalCount,
      totalPages: Math.ceil(totalCount / pageLimit),
    };

    sendResponse(res, 200, "User fetched successfully", true, data);
    return;
  } catch (error: any) {
    console.error(error);
    sendResponse(res, 500, error.message, false);
    return;
  }
});

export const getUsersById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Fetch user by ID
      const user = await db(TABLE.USERS)
        .select(
          "id",
          "first_name",
          "last_name",
          "email",
          "role",
          "is_active",
          "is_verified"
        )
        .where("id", id)
        .first();

      if (!user) {
        return sendResponse(res, 400, "User not found", false);
      }

      sendResponse(res, 200, "User fetched successfully", true, user);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);

export const destroyUserById = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Find the user to delete
      const user = await db(TABLE.USERS).where("id", id).first();

      if (!user) {
        return sendResponse(res, 400, "User not found", false);
      }

      // Delete user from the database
      await db(TABLE.USERS).where("id", id).del();

      sendResponse(res, 200, "User deleted successfully", user);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
export const getAllDoctors = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    try {
      // Step 1: Get the 'doctor' role ID
      const doctorRole = await db(TABLE.ROLES)
        // .select("id")
        .where("role_name", "doctor")
        .first();

      if (!doctorRole) {
        sendResponse(res, 500, "Role 'doctor' not found in roles table", false);
        return;
      }

      // Step 2: Fetch users with role_id = doctorRole.id
      const doctors = await db(TABLE.USERS)
        .leftJoin(TABLE.ROLES, "users.role_id", "roles.id") // join to fetch role name
        .select(
          "users.id",
          "users.first_name",
          "users.last_name",
          "users.email",
          "users.specialist_id",
          "users.is_active",
          "users.username", 
          "users.is_verified",
          "users.created_at",
          "roles.role_name as role" 
        )
        .where("users.role_id", doctorRole.id)
        .orderBy("users.created_at", "desc");

      // Step 3: Return result
      sendResponse(res, 200, "Doctors fetched successfully", true, doctors);
    } catch (error: any) {
      console.error(error);
      sendResponse(res, 500, error.message, false);
    }
  }
);
